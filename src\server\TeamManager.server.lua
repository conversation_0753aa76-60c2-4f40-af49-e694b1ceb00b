-- TeamManager.server.lua

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local Bindables = ServerStorage:WaitForChild("Bindables")
local MatchManager = require(ReplicatedStorage.Shared:WaitF<PERSON><PERSON>hild("MatchManager"))

local matchStart_BE = ServerStorage.Bindables:WaitForChild("Match_Start_Event")
local matchEnd_BE = ServerStorage.Bindables:WaitForChild("Match_End_Event")

-- Teams folders
local TeamsFolder = ReplicatedStorage:WaitForChild("Teams")
local RedTeamFolder = TeamsFolder:WaitForChild("Red")
local BlueTeamFolder = TeamsFolder:WaitForChild("Blue")

-- Team models in Workspace
local RedTeamModel = workspace.Table:WaitForChild("RedTeam")
local BlueTeamModel = workspace.Table:WaitForChild("BlueTeam")

-- Prefabs folder for GUI
local Prefabs = ReplicatedStorage:WaitF<PERSON><PERSON>hild("Prefabs")
local TeamGUI = Prefabs:WaitFor<PERSON>hild("TeamFrame") -- This is the GUI frame

-- Tracking
local modelOccupied = {}    -- [model] = player
local playerDebounce = {}   -- [player] = true if already triggered
local playerModel = {}      -- [player] = model they joined

local function assignPlayerToTeam(player, teamFolder, model)
    if modelOccupied[model] or playerDebounce[player] then
        return
    end

    -- Prevent multiple GUI clones
    if player:FindFirstChild("PlayerGui"):FindFirstChild("TeamFrame") then
        return
    end

    local config = teamFolder:FindFirstChild("TeamConfig")
    local colorValue = config and config:FindFirstChild("TeamColor")
    if colorValue and colorValue:IsA("BrickColorValue") then
        player.TeamColor = colorValue.Value
        modelOccupied[model] = player
        playerDebounce[player] = true
        playerModel[player] = model
        print(player.Name .. " joined team " .. teamFolder.Name .. " with color " .. tostring(player.TeamColor))

        -- Update MatchManager
        if teamFolder.Name == "Red" then
            MatchManager.AddPlayerToTeam(player, "Red")
        elseif teamFolder.Name == "Blue" then
            MatchManager.AddPlayerToTeam(player, "Blue")
        end

        -- Clone GUI to player
        local guiClone = TeamGUI:Clone()
        guiClone.Parent = player:WaitForChild("PlayerGui")

        -- Connect the button
        local leaveButton = guiClone.Frame:FindFirstChild("LeaveButton", true)
        if leaveButton and leaveButton:IsA("TextButton") then
            leaveButton.MouseButton1Click:Connect(function()
                -- Remove player from team
                player.TeamColor = BrickColor.White()
                playerDebounce[player] = nil
                modelOccupied[model] = nil
                playerModel[player] = nil
                MatchManager.RemovePlayerFromTeams(player)
                guiClone:Destroy()
                print(player.Name .. " left team manually")
            end)
        end
    else
        warn("No TeamColor found in TeamConfig for " .. teamFolder.Name)
    end
end

-- Setup Touched events
local function setupTeamModel(model, teamFolder)
    for _, part in pairs(model:GetDescendants()) do
        if part:IsA("BasePart") then
            part.Touched:Connect(function(hit)
                local player = Players:GetPlayerFromCharacter(hit.Parent)
                if player then
                    assignPlayerToTeam(player, teamFolder, model)
                end
            end)
        end
    end
end

setupTeamModel(RedTeamModel, RedTeamFolder)
setupTeamModel(BlueTeamModel, BlueTeamFolder)

-- Function to hide leave button for all players
local function hideLeaveButtons()
    for _, player in pairs(Players:GetPlayers()) do
        local playerGui = player:FindFirstChild("PlayerGui")
        if playerGui then
            local teamGui = playerGui:FindFirstChild("TeamFrame")
            if teamGui and teamGui.Frame then
                local leaveButton = teamGui.Frame:FindFirstChild("LeaveButton", true)
                if leaveButton then
                    leaveButton.Visible = false
                    print("Leave button hidden for "..player.Name)
                end
            end
        end
    end
end

matchStart_BE.Event:Connect(function()
    -- Call original MatchStart logic (if it exists in MatchManager)
    if MatchManager.MatchStart then
        MatchManager.MatchStart()
    end

    -- Now run your extra logic
    hideLeaveButtons()
end)

local function clearModelTeams()
    for model, player in pairs(modelOccupied) do
        if player and player.Parent then
            -- Reset team color
            player.TeamColor = BrickColor.White()
            -- Destroy GUI if exists
            local playerGui = player:FindFirstChild("PlayerGui")
            if playerGui then
                local gui = playerGui:FindFirstChild("TeamFrame")
                if gui then gui:Destroy() end
            end
            print(player.Name .. " removed from model "..model.Name.." after match end")
        end
        -- Clear model slot
        modelOccupied[model] = nil
        -- Clear debounce
        if player then
            playerDebounce[player] = nil
            playerModel[player] = nil
            MatchManager.RemovePlayerFromTeams(player)
        end
    end
end

-- Hook into match end (server bindable)
local MatchEndBindable = game.ServerStorage.Bindables:WaitForChild("Match_End_Event")

MatchEndBindable.Event:Connect(function()
    if MatchManager.MatchEnd then
        MatchManager.MatchEnd()
    end

    clearModelTeams()
end)