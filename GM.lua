-- GameHandler.server.lua (multi-table)
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local TableMatchManager = require(ReplicatedStorage.Shared:WaitFor<PERSON>hild("TableMatchManager"))

-- Remotes (create these under ReplicatedStorage/Remotes)
local Remotes = ReplicatedStorage:WaitForChild("Remotes")
local RE_TimerUpdate      = Remotes:WaitForChild("TimerUpdate")      -- :FireAllClients(tableId, timeLeft)
local RE_MatchStartEvent  = Remotes:WaitForChild("MatchStartEvent")  -- :FireAllClients(tableId)
local RE_MatchEndEvent    = Remotes:WaitForChild("MatchEndEvent")    -- :FireAllClients(tableId)
local RF_RequestJoinTeam  = Remotes:WaitForChild("RequestJoinTeam")  -- RemoteFunction(player, tableId, "Red"/"Blue") -> success,msg
local RE_TeamAssigned     = Remotes:WaitForChild("TeamAssigned")     -- :FireClient(player, tableId, teamName)

-- === Find all table models ===
-- Prefer a folder named "Tables"; fall back to every model named "Table"
local function findTables()
	local container = workspace:FindFirstChild("Tables")
	if container then
		return container:GetChildren()
	end
	local list = {}
	for _, d in ipairs(workspace:GetChildren()) do
		if d:IsA("Model") and d.Name == "Table" then
			table.insert(list, d)
		end
	end
	return list
end

-- Build per-table managers and UI references
local MatchesById = {}          -- [tableId] = TableMatchManager
local ServerBoards = {}         -- [tableId] = { timerLabel = TextLabel, redLabel?, blueLabel? }

local function grabBoardRefs(tbl: Model)
	-- Adjust these paths to match your model:
	-- Board > "Timer/Points" (SurfaceGui) > Frame > Timer(TextLabel)
	local boardSurface = tbl:WaitForChild("Board"):WaitForChild("Timer/Points")
	local frame = boardSurface:WaitForChild("Frame")
	local timerLabel = frame:WaitForChild("Timer")
	local redPoints = frame:FindFirstChild("RedPoints")  -- optional if you add it
	local bluePoints = frame:FindFirstChild("BluePoints")
	return { timerLabel = timerLabel, redLabel = redPoints, blueLabel = bluePoints }
end

for _, tbl in ipairs(findTables()) do
	local mgr = TableMatchManager.new(tbl)
	local id = mgr:GetId()
	MatchesById[id] = mgr
	ServerBoards[id] = grabBoardRefs(tbl)

	-- wire callbacks
	mgr.onStart = function()
		RE_MatchStartEvent:FireAllClients(id)
	end
	mgr.onTick = function(timeLeft)
		RE_TimerUpdate:FireAllClients(id, timeLeft)
		local ui = ServerBoards[id]
		if ui and ui.timerLabel then
			local m = math.floor(math.max(0, timeLeft)/60)
			local s = math.floor(math.max(0, timeLeft)%60)
			ui.timerLabel.Text = string.format("%02d:%02d", m, s)
		end
	end
	mgr.onEnd = function()
		RE_MatchEndEvent:FireAllClients(id)
		local ui = ServerBoards[id]
		if ui and ui.timerLabel then
			ui.timerLabel.Text = "00:00"
		end
	end
end

-- === Team Join Handling (cap 1 per team per table) ===
RF_RequestJoinTeam.OnServerInvoke = function(player, tableId: string, teamName: string)
	local mgr = MatchesById[tableId]
	if not mgr then
		return false, "Unknown table"
	end
	if teamName ~= "Red" and teamName ~= "Blue" then
		return false, "Invalid team"
	end
	local ok, msg = mgr:TryJoin(player, teamName)
	if not ok then
		return false, msg
	end
	-- tell the player which table/team they are bound to
	RE_TeamAssigned:FireClient(player, tableId, teamName)

	-- Auto-start when both seats are filled
	if mgr:CanStart() then
		mgr:Start(180) -- 3 minutes per match (change as you like)
	end
	return true, "Joined"
end

-- Clean up if a player leaves the game
Players.PlayerRemoving:Connect(function(p)
	for _, mgr in pairs(MatchesById) do
		if mgr:TryLeave(p) then
			-- if a match was waiting to start, do nothing;
			-- if it was running, you can decide to end early:
			if mgr.active then
				mgr:End()
			end
		end
	end
end)
