# Ping Pong Master
Generated by [Ro<PERSON>](https://github.com/rojo-rbx/rojo) 7.4.4.

## Getting Started
To build the place from scratch, use:

```bash
rojo build -o "Ping Pong Master.rbxlx"
```

Next, open `Ping Pong Master.rbxlx` in Roblox Studio and start the Rojo server:

```bash
rojo serve
```

For more help, check out [the Rojo documentation](https://rojo.space/docs).

## Tooling

This project includes optional tooling to improve development workflow.

1) Install Wally dependencies (TestEZ, Moonwave, Roact already declared):

```powershell
wally install
```

2) Run Luacheck linting locally:

```powershell
luacheck src --config .luacheckrc
```

3) Run tests in Roblox Studio:

- Open the place generated by `rojo build -o "Ping Pong Master.rbxlx"` in Roblox Studio or run `rojo serve` and attach.
- Ensure Wally packages are exported into `ReplicatedStorage.WallyPackages` (or adjust `RunTests.server.lua` paths).
- Play the game in Studio; `RunTests.server.lua` will attempt to run TestEZ and print results to the output.

4) Generate docs with Moon<PERSON> (if installed and configured):

```powershell
moonwave docs
```
