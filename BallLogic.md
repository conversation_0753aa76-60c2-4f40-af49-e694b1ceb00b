local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local PathfindingService = game:GetService("PathfindingService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local OrbModule = {}
OrbModule.__index = OrbModule

local ORB_HEALTH = 1
local ORB_SPEED = 40
local ORB_RADIUS = 2
local PATH_RECALC_TIME = 1
local KNOCKBACK_FORCE = 50
local KNOCKBACK_DURATION = 0.35

local orbPrefab = ReplicatedStorage:WaitForChild("OrbEntity")

local function debugLog(tag, ...)
    print("[OrbModule][" .. tag .. "]", ...)
end

local function warnIfNil(tag, value, name)
    if value == nil then
        warn("[OrbModule][WARN][" .. tag .. "] " .. name .. " is nil!")
    end
end

local function getNearestPlayer<PERSON>haracter(position)
    for _, player in ipairs(Players:GetPlayers()) do
        local character = player.Character
        if character and character:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart") then
            if (character.HumanoidRootPart.Position - position).Magnitude < 80 then
                return character
            end
        end
    end
    return nil
end

function OrbModule.new(spawnPosition, destroyCallback)
    local self = setmetatable({}, OrbModule)

    local orbModel = orbPrefab:Clone()
    orbModel.Name = "OrbEntity"
    orbModel.Parent = workspace

    self.Model = orbModel
    self.RootPart = orbModel.PrimaryPart
    self.Humanoid = orbModel:FindFirstChild("Humanoid")
    self.HumanoidRootPart = orbModel:FindFirstChild("HumanoidRootPart")
    self.Hitbox = orbModel:FindFirstChild("Hitbox")

    self.Health = ORB_HEALTH
    self.Destroyed = false
    self.DestroyCallback = destroyCallback

    self._moveConn = nil
    self._heartbeatConn = nil
    self._touchConn = nil
    self._knockbackActive = false

    warnIfNil("Init", self.Model, "Model")
    warnIfNil("Init", self.RootPart, "PrimaryPart")
    warnIfNil("Init", self.Humanoid, "Humanoid")
    warnIfNil("Init", self.HumanoidRootPart, "HumanoidRootPart")
    warnIfNil("Init", self.Hitbox, "Hitbox")

    if self.HumanoidRootPart then
        orbModel:SetPrimaryPartCFrame(CFrame.new(spawnPosition))
        self._lastPosition = self.HumanoidRootPart.Position
        self._lastMoveTime = tick()
    else
        warn("[OrbModule][Init] Cannot set orb position: HumanoidRootPart is missing.")
        self._lastPosition = Vector3.new()
        self._lastMoveTime = tick()
    end

    if self.Humanoid then
        self.Humanoid.WalkSpeed = ORB_SPEED
    end

    if self.Hitbox then
        self._touchConn = self.Hitbox.Touched:Connect(function(hit)
            self:OnHit(hit)
        end)
    end

    self.Moving = false
    self._currentPath = nil
    self._currentWaypoint = 0
    self._lastTarget = nil
    self._lastPathTime = 0

    self._heartbeatConn = RunService.Heartbeat:Connect(function()
        if self.Destroyed or not self.HumanoidRootPart or self._knockbackActive then return end

        local pos = self.HumanoidRootPart.Position
        if (pos - self._lastPosition).Magnitude > 0.5 then
            self._lastMoveTime = tick()
            self._lastPosition = pos
        end

        -- Force repath if stuck (enhanced timeout to 1.5 seconds)
        if self.Moving and tick() - self._lastMoveTime > 1.5 then
            debugLog("Stuck", "Orb seems stuck, forcing repath")
            self.Moving = false
        end

        -- Regular repath logic
        local target = getNearestPlayerCharacter(pos)
        if target and (not self.Moving or tick() - self._lastPathTime > PATH_RECALC_TIME) then
            local targetPos = target:FindFirstChild("HumanoidRootPart") and target.HumanoidRootPart.Position
            if targetPos then
                self:MoveAlongPath(pos, targetPos)
                self._lastPathTime = tick()
                self._lastTarget = target
            end
        end
    end)

    return self
end

function OrbModule:MoveAlongPath(startPos, targetPos)
    local path = PathfindingService:CreatePath({
        AgentRadius = ORB_RADIUS,
        AgentHeight = 5,
        AgentCanJump = true
    })
    path:ComputeAsync(startPos, targetPos)

    if path.Status ~= Enum.PathStatus.Success then
        self.Moving = false
        return
    end

    local waypoints = path:GetWaypoints()
    if #waypoints == 0 then
        self.Moving = false
        return
    end

    self.Moving = true
    self._currentWaypoint = 2
    self._currentPath = waypoints

    -- Disconnect old
    if self._moveConn then self._moveConn:Disconnect() end

    -- Heartbeat-driven smooth move
    self._moveConn = RunService.Heartbeat:Connect(function()
        if self.Destroyed or self._knockbackActive then return end
        if not self._currentPath or self._currentWaypoint > #self._currentPath then
            self.Moving = false
            return
        end

        local nextWaypoint = self._currentPath[self._currentWaypoint]
        local dist = (self.HumanoidRootPart.Position - nextWaypoint.Position).Magnitude

        -- If close enough, go to next
        if dist < 2 then
            self._currentWaypoint += 1
            if self._currentWaypoint <= #self._currentPath then
                nextWaypoint = self._currentPath[self._currentWaypoint]
                self.Humanoid:MoveTo(nextWaypoint.Position)
            else
                self.Moving = false
                return
            end
        else
            -- Keep moving toward it
            self.Humanoid:MoveTo(nextWaypoint.Position)
        end
    end)

    -- Start first move
    self.Humanoid:MoveTo(waypoints[self._currentWaypoint].Position)
end

function OrbModule:OnHit(hit)
    if self.Destroyed then return end

    local attackingChar = hit:FindFirstAncestorOfClass("Model")
    if not attackingChar then return end

    local humanoid = attackingChar:FindFirstChild("Humanoid")
    if not humanoid or humanoid.Health <= 0 then return end

    local tool = hit:FindFirstAncestorWhichIsA("Tool")
    if not tool then return end

    debugLog("Hit", "Orb hit by tool from", attackingChar.Name)

    if self.HumanoidRootPart and attackingChar:FindFirstChild("HumanoidRootPart") then
        local attackerHRP = attackingChar.HumanoidRootPart
        self:TakeDamage(1, attackerHRP.Position)
    else
        self:TakeDamage(1)
    end
end

function OrbModule:ApplyKnockback(sourcePosition)
    if not self.HumanoidRootPart then return end

    -- Stop movement so Heartbeat will repath
    self.Moving = false

    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.Velocity = (self.HumanoidRootPart.Position - sourcePosition).Unit * 30 + Vector3.new(0, 15, 0)
    bodyVelocity.MaxForce = Vector3.new(100000, 100000, 100000)
    bodyVelocity.Parent = self.HumanoidRootPart

    self._knockbackActive = true

    task.delay(KNOCKBACK_DURATION, function()
        if bodyVelocity then bodyVelocity:Destroy() end
        self._knockbackActive = false

        -- Force immediate repath after knockback
        local target = getNearestPlayerCharacter(self.HumanoidRootPart.Position)
        if target and target:FindFirstChild("HumanoidRootPart") then
            self:MoveAlongPath(self.HumanoidRootPart.Position, target.HumanoidRootPart.Position)
            self._lastTarget = target
            self._lastPathTime = tick()
        end
    end)
end

function OrbModule:TakeDamage(amount, sourcePosition)
    if self.Destroyed then return end
    self.Health = self.Health - amount

    print("[OrbModule][Damage] Taking damage | Current HP:", self.Health)

    if sourcePosition then
        self:ApplyKnockback(sourcePosition)
    end

    if self.Health <= 0 then
        self:Destroy()
    end
end

function OrbModule:Destroy()
    if self.Destroyed then return end
    debugLog("Destroy", "Destroying orb.")

    self.Destroyed = true

    if self._heartbeatConn then self._heartbeatConn:Disconnect() end
    if self._moveConn then self._moveConn:Disconnect() end
    if self._touchConn then self._touchConn:Disconnect() end

    if self.Humanoid then
        self.Humanoid:ChangeState(Enum.HumanoidStateType.Physics)
        self.Humanoid.AutoRotate = false
        self.Humanoid.WalkSpeed = 0
    end

    for _, part in self.Model:GetDescendants() do
        if part:IsA("Motor6D") then
            part:Destroy()
        elseif part:IsA("BasePart") then
            part.Anchored = false
            part.CanCollide = true
        end
    end

    if typeof(self.DestroyCallback) == "function" then
        debugLog("Destroy", "Calling destroy callback")
        self.DestroyCallback()
    end

    task.delay(5, function()
        if self.Model and self.Model.Parent then
            debugLog("Destroy", "Delayed cleanup of model")
            self.Model:Destroy()
        end
    end)
end

return OrbModule
