-- GameManager.server.lua (Multi-Table System)

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local RunService = game:GetService("RunService")

local TableMatchManager = require(ReplicatedStorage.Shared:WaitForChild("TableMatchManager"))

-- RemoteEvents for clients (inside ReplicatedStorage.Remotes)
local Remotes = ReplicatedStorage:WaitForChild("Remotes")

-- Get existing remotes or create new ones for multi-table system
local RE_TimerUpdate = Remotes:FindFirstChild("TimerUpdate") or Remotes:WaitForChild("TimerUpdate")
local RE_MatchStartEvent = Remotes:FindFirstChild("MatchStartEvent") or Remotes:WaitForChild("MatchStartEvent")
local RE_MatchEndEvent = Remotes:FindFirstChild("MatchEndEvent") or Remotes:WaitForChild("MatchEndEvent")

-- Create new remotes for multi-table team joining system
local RF_RequestJoinTeam = Remotes:FindFirstChild("RequestJoinTeam")
if not RF_RequestJoinTeam then
	RF_RequestJoinTeam = Instance.new("RemoteFunction")
	RF_RequestJoinTeam.Name = "RequestJoinTeam"
	RF_RequestJoinTeam.Parent = Remotes
	print("✅ Created RequestJoinTeam RemoteFunction")
end

local RE_TeamAssigned = Remotes:FindFirstChild("TeamAssigned")
if not RE_TeamAssigned then
	RE_TeamAssigned = Instance.new("RemoteEvent")
	RE_TeamAssigned.Name = "TeamAssigned"
	RE_TeamAssigned.Parent = Remotes
	print("✅ Created TeamAssigned RemoteEvent")
end

-- === Find all table models ===
-- Prefer a folder named "Tables"; fall back to every model named "Table"
local function findTables()
	local container = workspace:FindFirstChild("Tables")
	if container then
		return container:GetChildren()
	end
	local list = {}
	for _, d in ipairs(workspace:GetChildren()) do
		if d:IsA("Model") and d.Name == "Table" then
			table.insert(list, d)
		end
	end
	return list
end

-- Build per-table managers and UI references
local MatchesById = {}          -- [tableId] = TableMatchManager
local ServerBoards = {}         -- [tableId] = { timerLabel = TextLabel, redLabel?, blueLabel? }

local function grabBoardRefs(tbl: Model)
	-- Adjust these paths to match your model:
	-- Board > "Timer/Points" (SurfaceGui) > Frame > Timer(TextLabel)
	local boardSurface = tbl:WaitForChild("Board"):WaitForChild("Timer/Points")
	local frame = boardSurface:WaitForChild("Frame")
	local timerLabel = frame:WaitForChild("Timer")
	local redPoints = frame:FindFirstChild("RedPoints")  -- optional if you add it
	local bluePoints = frame:FindFirstChild("BluePoints")
	return { timerLabel = timerLabel, redLabel = redPoints, blueLabel = bluePoints }
end

-- Initialize all tables
for _, tbl in ipairs(findTables()) do
	local mgr = TableMatchManager.new(tbl)
	local id = mgr:GetId()
	MatchesById[id] = mgr
	ServerBoards[id] = grabBoardRefs(tbl)

	-- Wire callbacks for each table
	mgr.onStart = function()
		RE_MatchStartEvent:FireAllClients(id)
		print("🟢 Match started on table:", id)
	end

	mgr.onTick = function(timeLeft)
		RE_TimerUpdate:FireAllClients(id, timeLeft)
		local ui = ServerBoards[id]
		if ui and ui.timerLabel then
			local m = math.floor(math.max(0, timeLeft)/60)
			local s = math.floor(math.max(0, timeLeft)%60)
			ui.timerLabel.Text = string.format("%02d:%02d", m, s)
		end
	end

	mgr.onEnd = function()
		RE_MatchEndEvent:FireAllClients(id)
		local ui = ServerBoards[id]
		if ui and ui.timerLabel then
			ui.timerLabel.Text = "00:00"
		end
		print("🔴 Match ended on table:", id)
	end
end

print("✅ Initialized", #findTables(), "ping pong tables")

-- === Team Join Handling (cap 1 per team per table) ===
RF_RequestJoinTeam.OnServerInvoke = function(player, tableId: string, teamName: string)
	local mgr = MatchesById[tableId]
	if not mgr then
		return false, "Unknown table"
	end
	if teamName ~= "Red" and teamName ~= "Blue" then
		return false, "Invalid team"
	end
	local ok, msg = mgr:TryJoin(player, teamName)
	if not ok then
		return false, msg
	end
	-- Tell the player which table/team they are bound to
	RE_TeamAssigned:FireClient(player, tableId, teamName)
	print("👤", player.Name, "joined", teamName, "team on table", tableId)

	-- Auto-start when both seats are filled
	if mgr:CanStart() then
		mgr:Start(180) -- 3 minutes per match (change as needed)
	end
	return true, "Joined"
end

-- Clean up if a player leaves the game
Players.PlayerRemoving:Connect(function(player)
	for tableId, mgr in pairs(MatchesById) do
		if mgr:TryLeave(player) then
			print("👋", player.Name, "left table", tableId)
			-- If a match was waiting to start, do nothing;
			-- If it was running, you can decide to end early:
			if mgr.active then
				mgr:End()
				print("⚠️ Match on table", tableId, "ended early due to player leaving")
			end
		end
	end
end)
