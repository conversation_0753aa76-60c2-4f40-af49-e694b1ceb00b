-- GameManager.server.lua

-- ServerScriptService/GameHandler
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local RunService = game:GetService("RunService")
local MatchManager = require(ReplicatedStorage.Shared:WaitFor<PERSON>hild("MatchManager"))
local TableMatchManager = require(ReplicatedStorage.Shared:WaitForChild("TableMatchManager"))

-- BindableEvents from MatchManager (server-only)
local MatchStart_BE = ServerStorage.Bindables:WaitForChild("Match_Start_Event") -- BindableEvent
local MatchEnd_BE   = ServerStorage.Bindables:WaitForChild("Match_End_Event")   -- BindableEvent

-- RemoteEvents for clients (inside ReplicatedStorage.Remotes)
local Remotes = ReplicatedStorage:WaitForChild("Remotes")
local RE_MatchStart  = Remotes:WaitForChild("MatchStartEvent")
local RE_MatchEnd    = Remotes:WaitForChild("MatchEndEvent")
local RE_TimerUpdate = Remotes:WaitForChild("TimerUpdate")

-- Server-side board timer label in Workspace
local board = workspace.Table.Board["Timer/Points"].Frame
local timerLabel = board:WaitForChild("Timer")

local broadcasterThread = nil

local function fmt(t)
	t = math.max(0, t or 0)
	local m = math.floor(t/60)
	local s = math.floor(t%60)
	return string.format("%02d:%02d", m, s)
end

local function startBroadcasting()
	if broadcasterThread then return end
	broadcasterThread = task.spawn(function()
		while MatchManager.matchActive do
			-- Send time to clients once per second
			RE_TimerUpdate:FireAllClients(MatchManager.matchTimer)
			-- Keep server board in sync too (once per second)
			timerLabel.Text = fmt(MatchManager.matchTimer)
			task.wait(1)
		end
		broadcasterThread = nil
	end)
end

-- Relay MatchManager events to clients + kick off timer broadcasting
MatchStart_BE.Event:Connect(function()
	RE_MatchStart:FireAllClients()
	startBroadcasting()
end)

MatchEnd_BE.Event:Connect(function()
	RE_MatchEnd:FireAllClients()
	timerLabel.Text = "00:00"
end)

-- Smooth board updates between 1s ticks
RunService.Heartbeat:Connect(function()
	if MatchManager.matchActive then
		timerLabel.Text = fmt(MatchManager.matchTimer)
	end
end)
