-- Simple TestEZ runner for Roblox Studio
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local ok, TestEZ = pcall(function()
    return require(ReplicatedStorage:WaitForChild("WallyPackages"):Find<PERSON>irstChild("testez"))
end)

if not ok then
    warn("TestEZ not found. Install via Wally and ensure packages are exported to ReplicatedStorage.WallyPackages")
    return
end

local results = TestEZ.Runner.run({script.Parent:FindFirstChild("tests") or script.Parent}, TestEZ.Reporters.TextReporter)
for _, result in ipairs(results) do
    print(result)
end
