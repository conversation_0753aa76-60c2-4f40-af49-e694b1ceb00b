-- Join <PERSON><PERSON> Script
-- Handles the menu gui whenever a player joins the game.

-- MenuHandler.client.lua
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Prefabs Location
local menuPrefab = ReplicatedStorage:WaitForChild("Prefabs"):WaitForChild("JoinMenu")

local menuInstance

-- Function to show menu
local function showMenu()
    -- Clone the GUI prefab
    local menuClone = menuPrefab:Clone()
    menuClone.Name = "JoinMenu"
    menuClone.Parent = playerGui
end

-- Function to hide menu 
local function hideMenu()
    local existingMenu = playerGui:FindFirstChild("JoinMenu")
    if existingMenu then
        existingMenu:Destroy()
    end
end

-- Detect player input (mouse click or touch)
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end -- Ignore clicks for UI buttons
    if input.UserInputType == Enum.UserInputType.MouseButton1 
    or input.UserInputType == Enum.UserInputType.Touch then
        hideMenu()
    end
end)

-- Show menu when player joins
showMenu()

task.delay(30, hideMenu)
