-- TableMatchManager.lua
-- One instance of this object per physical table model.

local TableMatchManager = {}
TableMatchManager.__index = TableMatchManager

export type Match = {
	tableModel: Model,
	timer: number,
	active: boolean,
	points: {Red: number, Blue: number},
	players: {Red: Player?, Blue: Player?},
	heartbeat: RBXScriptConnection?,
	broadcast: RBXScriptConnection?,
	onTick: (timeLeft: number) -> (),
	onStart: () -> (),
	onEnd: () -> (),
}

function TableMatchManager.new(tableModel: Model)
	local self: Match = setmetatable({}, TableMatchManager)
	self.tableModel = tableModel
	self.timer = 0
	self.active = false
	self.points = { Red = 0, Blue = 0 }
	self.players = { Red = nil, Blue = nil }
	self.heartbeat = nil
	self.onTick = function(_) end
	self.onStart = function() end
	self.onEnd = function() end
	return self
end

function TableMatchManager:GetId()
	-- Stable id for remotes/clients
	return self.tableModel.Name
end

function TableMatchManager:IsFull(teamName: "Red"|"Blue")
	return self.players[teamName] ~= nil
end

function TableMatchManager:Try<PERSON>oin(player: Player, teamName: "Red"|"Blue"): (boolean, string)
	if self.active then
		return false, "Match already running"
	end
	if self.players[teamName] then
		return false, "That team is full"
	end
	-- prevent same player in both teams
	for _, p in pairs(self.players) do
		if p == player then
			return false, "You already joined this table"
		end
	end
	self.players[teamName] = player
	return true, "Joined"
end

function TableMatchManager:TryLeave(player: Player)
	for k, p in pairs(self.players) do
		if p == player then
			self.players[k] = nil
			return true
		end
	end
	return false
end

function TableMatchManager:CanStart()
	return self.players.Red ~= nil and self.players.Blue ~= nil
end

function TableMatchManager:Start(durationSeconds: number)
	if self.active then return end
	self.timer = math.max(0, durationSeconds or 180)
	self.points = { Red = 0, Blue = 0 }
	self.active = true
	self.onStart()
	-- Tick once per second (server authoritative)
	task.spawn(function()
		while self.active and self.timer > 0 do
			task.wait(1)
			self.timer -= 1
			self.onTick(self.timer)
		end
		if self.active then
			self:End()
		end
	end)
end

function TableMatchManager:End()
	if not self.active then return end
	self.active = false
	self.timer = 0
	self.players = { Red = nil, Blue = nil } -- clear seats
	self.onEnd()
end

function TableMatchManager:AddPoint(teamName: "Red"|"Blue", n: number?)
	self.points[teamName] += (n or 1)
end

return TableMatchManager
