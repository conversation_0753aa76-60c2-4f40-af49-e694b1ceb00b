name: CI

on: [push, pull_request]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install LuaRocks
        run: sudo apt-get update && sudo apt-get install -y luarocks lua5.3
      - name: Install Luacheck
        run: luarocks install luacheck
      - name: Run Luacheck
        run: luacheck src --config .luacheckrc || true
      - name: Wally install
        run: |
          curl -sSL https://raw.githubusercontent.com/UpliftGames/wally/main/install.sh | bash -s -- -b .wally
          export PATH="$PWD/.wally:$PATH"
          .wally/wally install
      - name: Run tests (placeholder)
        run: echo "Test execution requires Roblox environment; run tests locally in Studio or with a dedicated runner"
