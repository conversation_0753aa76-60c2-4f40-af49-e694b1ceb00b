-- C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (LocalScript inside StarterPlayerScripts)
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local Remotes = ReplicatedStorage:WaitForChild("Remotes")
local RE_TimerUpdate = Remotes:WaitForChild("TimerUpdate")
local RE_MatchStartEvent = Remotes:WaitForChild("MatchStartEvent")
local RE_MatchEndEvent = Remotes:WaitForChild("MatchEndEvent")

-- Prefabs setup
local prefabsFolder = ReplicatedStorage:WaitForChild("Prefabs")
local blacklist = {
    ["JoinMenu"] = true,
    ["TeamFrame"] = true, 
}

-- Store cloned GUIs so we can toggle them later
local clonedGuis = {}

-- Clone GUIs for the local player
local function cloneGUIsForLocalPlayer()
    for _, guiPrefab in ipairs(prefabsFolder:GetChildren()) do
        if guiPrefab:IsA("ScreenGui") and not blacklist[guiPrefab.Name] then
            if not playerGui:FindFirstChild(guiPrefab.Name) then
                local clone = guiPrefab:Clone()
                clone.Parent = playerGui
                clone.Enabled = false
                clonedGuis[clone.Name] = clone
                print("✅ Cloned GUI:", clone.Name)
            end
        end
    end
end

-- Handle enabling/disabling GUIs depending on JoinMenu
local function updateGuiStates()
    local joinMenu = playerGui:FindFirstChild("JoinMenu")
    local enabled = joinMenu == nil
    for _, gui in pairs(clonedGuis) do
        gui.Enabled = enabled
    end
end

-- Listen for JoinMenu being added/removed
playerGui.ChildAdded:Connect(function(child)
    if child.Name == "JoinMenu" then
        updateGuiStates()
    end
end)

playerGui.ChildRemoved:Connect(function(child)
    if child.Name == "JoinMenu" then
        updateGuiStates()
    end
end)

-- Clone GUIs on startup
cloneGUIsForLocalPlayer()
updateGuiStates()

-- Client Timer GUI setup
local clientTimerGui = playerGui:WaitForChild("ClientTimer")
local clientTimerLabel = clientTimerGui.Frame:WaitForChild("Timer")
clientTimerGui.Enabled = false

-- Listen for updates from the server
RE_TimerUpdate.OnClientEvent:Connect(function(data)
    if data == "end" then
        clientTimerGui.Enabled = false
        clientTimerLabel.Text = "00:00"
        print("⏹️ Timer hidden (match ended).")
    else
        clientTimerGui.Enabled = true
        local timeRemaining = math.max(0, data)
        local m = math.floor(timeRemaining / 60)
        local s = math.floor(timeRemaining % 60)
        clientTimerLabel.Text = string.format("%02d:%02d", m, s)
    end
end)
