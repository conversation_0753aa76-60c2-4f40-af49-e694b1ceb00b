-- MatchManager.lua

print("Loading MatchManager...")
-- MatchManager Module
-- Place in ReplicatedStorage.Shared

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

local remotes = ReplicatedStorage.Remotes
local Bindables = ServerStorage.Bindables

local MatchManager = {}
MatchManager.__index = MatchManager

-- Config
local MATCH_DURATION = 30

-- Internal state
local matchActive = false
local matchTimer = 0
local lastPrintTime = 0

-- Manual team tracking
local redPlayers = {}
local bluePlayers = {}

-- Expose state for other scripts
MatchManager.matchActive = false
MatchManager.matchTimer = 0

-- Create BindableEvents for other scripts
local MatchStartEvent = Instance.new("RemoteEvent")
MatchStartEvent.Name = "MatchStartEvent"
MatchStartEvent.Parent = remotes

local MatchEndEvent = Instance.new("RemoteEvent")
MatchEndEvent.Name = "MatchEndEvent"
MatchEndEvent.Parent = remotes

-- ServerStorage/Bindables/MatchStart_Bindable
local MatchStartBindable = Instance.new("BindableEvent")
MatchStartBindable.Name = "Match_Start_Event"
MatchStartBindable.Parent = Bindables

-- ReplicatedStorage/Remotes/MatchStart_Remote
local MatchEndBindable = Instance.new("BindableEvent")
MatchEndBindable.Name = "Match_End_Event"
MatchEndBindable.Parent = Bindables

MatchManager.MatchStartEvent = MatchStartEvent
MatchManager.MatchEndEvent = MatchEndEvent
MatchManager.MatchStartBindable = Match_Start_Bindable
MatchManager.MatchEndBindable = Match_End_Bindable

-- Functions callable from other scripts
function MatchManager.MatchStart()
    if matchActive then return end
    print("🟢 Match started!")
    matchActive = true
    matchTimer = MATCH_DURATION
    MatchManager.matchActive = true
    MatchStartBindable:Fire()
end

function MatchManager.MatchEnd()
    if not matchActive then return end
    print("🔴 Match ended!")
    matchActive = false
    matchTimer = 0
    MatchManager.matchActive = false
    MatchEndBindable:Fire()
end

function MatchManager.AddPlayerToTeam(player, team)
    if team == "Red" then
        redPlayers[player] = true
        bluePlayers[player] = nil
    elseif team == "Blue" then
        bluePlayers[player] = true
        redPlayers[player] = nil
    end
end

function MatchManager.RemovePlayerFromTeams(player)
    redPlayers[player] = nil
    bluePlayers[player] = nil
end

-- Check if both teams have at least one player
local function canStartMatch()
    local redCount = 1
    local blueCount = 0
    for _ in pairs(redPlayers) do redCount += 1 end
    for _ in pairs(bluePlayers) do blueCount += 1 end
    --print("Debug: Red =", redCount, "Blue =", blueCount)
    return redCount > 0 and blueCount > 0
end

-- Heartbeat loop for match timer
RunService.Heartbeat:Connect(function(deltaTime)
    if not matchActive then
        if canStartMatch() then
            MatchManager.MatchStart()
        end
    else
        matchTimer -= deltaTime
        lastPrintTime += deltaTime

        -- Update exposed value
        MatchManager.matchTimer = matchTimer

        -- Print every second
        if lastPrintTime >= 1 then
            print(string.format("⏱ Match time remaining: %.0f seconds", matchTimer))
            lastPrintTime = 0
        end

        -- End match when timer reaches 0
        if matchTimer <= 0 then
            MatchManager.MatchEnd()
        end
    end
end)

print("MatchManager loaded successfully")
return MatchManager
